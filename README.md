# Vector-Fire 🎯

**A Tactical Military Targeting Application & Ballistic Calculator for Mortars**

Vector-Fire is a professional military targeting application designed for tactical operations. Observers mark targets and send locations to mortar teams for automatic ballistic calculations.

## 🚀 **Live Demo**

**Try the demo directly in your browser:**
- **[Observer Screen](https://your-username.github.io/vector-fire/observer.html)** - Mark and send targets
- **[Mortar Screen](https://your-username.github.io/vector-fire/mortar.html)** - Receive targets and calculate firing solutions

*No installation required! Works entirely in your browser.*

## ✨ **Features**

### 🎯 **Observer Screen**
- **Interactive Map**: Click to mark target locations
- **Target Management**: Add multiple targets with force types and quantities
- **Real-time Coordinates**: Live grid coordinate display
- **Target Sharing**: Send targets to mortar teams
- **Professional UI**: Military-grade interface with accessibility features

### 🎯 **Mortar Screen**
- **Ballistic Calculator**: Automatic firing solutions for 60mm, 81mm, and 120mm mortars
- **Target Reception**: Receive targets from observer teams
- **Range Calculations**: Distance, bearing, and elevation calculations
- **Ammunition Selection**: Multiple round types (HE, Smoke, Illumination)
- **Visual Range Rings**: Display mortar range capabilities

### 🎯 **Enhanced UX Features**
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Accessibility**: Full keyboard navigation and screen reader support
- **Retro Mode**: Classic military terminal theme
- **Help System**: Contextual tooltips and guidance
- **Smooth Animations**: Professional visual feedback

## 🛠 **Technology Stack**

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Mapping**: Leaflet.js with OpenStreetMap
- **Design**: Modern CSS Grid/Flexbox with military color scheme
- **Accessibility**: WCAG 2.1 compliant with ARIA labels
- **Demo Mode**: Works without backend server for GitHub Pages

## 🎮 **How to Use the Demo**

### Observer Screen:
1. **Click on the map** to mark a target location
2. **Select force type** (Infantry, Tank, Artillery, etc.)
3. **Choose target type** (Personnel, Vehicle, Structure, etc.)
4. **Set quantity** and **add target**
5. **Send targets** to mortar teams

### Mortar Screen:
1. **Set mortar position** by clicking on the map
2. **Select mortar type** (60mm, 81mm, or 120mm)
3. **Choose ammunition type**
4. **Click "Refresh Targets"** to receive targets from observers
5. **Select a target** to see firing solution

## 🎯 **Demo Features**

- **No Server Required**: Uses localStorage for target sharing between screens
- **Realistic Data**: Authentic military unit types and ammunition
- **Professional Interface**: Military-grade UI suitable for tactical operations
- **Cross-Platform**: Works on any modern browser

## 🚀 **Local Development**

If you want to run with the full backend:

```bash
# Clone the repository
git clone https://github.com/your-username/vector-fire.git
cd vector-fire

# Start the backend server
cd fscs-backend
node server.js

# Open in browser
# Observer: http://localhost:3000/observer.html
# Mortar: http://localhost:3000/mortar.html
```

## 📱 **Mobile Support**

Vector-Fire is fully responsive and optimized for:
- **Desktop**: Full-featured experience
- **Tablet**: Touch-optimized interface
- **Mobile**: Compact layout with enhanced touch targets

## ♿ **Accessibility**

- **Keyboard Navigation**: Full Tab and arrow key support
- **Screen Readers**: ARIA labels and announcements
- **High Contrast**: Support for accessibility preferences
- **Focus Management**: Clear visual focus indicators

## 🎨 **Themes**

- **Modern Theme**: Professional military interface
- **Retro Mode**: Classic terminal-style display with green text

## 🔧 **Browser Compatibility**

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📄 **License**

This project is for educational and demonstration purposes.

## 🤝 **Contributing**

This is a portfolio project demonstrating modern web development techniques for military applications.

---

**Built with ❤️ for tactical operations and modern web development**
