/* --- Theme and General Styles --- */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap');

:root {
  /* Primary Colors - Tactical Military Palette */
  --primary-color: #2c3e50; /* Tactical slate gray */
  --primary-light: #34495e; /* Lighter tactical gray */
  --primary-dark: #1a252f; /* Darker tactical gray */

  /* Secondary Colors */
  --secondary-color: #3c4043; /* Gunmetal gray */
  --secondary-light: #5f6368; /* Lighter gunmetal */
  --secondary-dark: #202124; /* Darker gunmetal */

  /* Accent Colors */
  --accent-color: #4a5d23; /* Tactical olive */
  --accent-light: #5a6d2a; /* Lighter tactical olive */
  --accent-dark: #3a4a1c; /* Darker tactical olive */

  /* Feedback Colors */
  --danger-color: #8b2635; /* Muted tactical red */
  --danger-light: #a13042; /* Lighter tactical red */
  --danger-dark: #6d1e2a; /* Darker tactical red */

  --info-color: #2c5282; /* Muted tactical blue */
  --info-light: #3182ce; /* Lighter tactical blue */
  --info-dark: #1a365d; /* Darker tactical blue */

  --action-color: #975a16; /* Muted tactical amber */
  --action-light: #b7791f; /* Lighter tactical amber */
  --action-dark: #744310; /* Darker tactical amber */
  --action-color-rgb: 151, 90, 22; /* RGB values for action color */

  --success-color: #4a5d23; /* Tactical olive */
  --warning-color: #975a16; /* Muted tactical amber */

  /* Background Colors */
  --background-light: #f7f8fc; /* Very light tactical gray */
  --background-medium: #e2e8f0; /* Light tactical gray */
  --surface-color: #ffffff; /* White for cards/containers */

  /* Military-inspired accent colors */
  --military-olive: #4b5320;
  --military-olive-light: #5c6627;
  --military-olive-dark: #3a4119;
  --military-tan: #c6b793;
  --military-brown: #5d4037;
  --military-gray: #546e7a;

  /* Text Colors */
  --text-dark: #1a2530; /* Very dark blue-gray */
  --text-medium: #37474f; /* Medium blue-gray */
  --text-light: #ffffff; /* White */
  --text-muted: #607d8b; /* Muted text */

  /* Border Colors */
  --border-color: #cfd8dc; /* Light blue-gray border */
  --border-color-hover: #b0bec5; /* Slightly darker on hover */
  --border-accent: #4b5320; /* Military olive for accents */

  /* UI Properties - Angular/Tactical Design */
  --border-radius-sm: 2px;
  --border-radius: 3px;
  --border-radius-lg: 4px;
  --border-radius-xl: 6px;

  /* Typography */
  --font-family-ui: 'Roboto', 'Segoe UI', -apple-system, BlinkMacSystemFont, Arial, sans-serif; /* Clean, professional UI font */
  --font-family-data: 'Roboto Mono', 'Consolas', 'Courier New', monospace; /* Monospaced font for data */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Enhanced Spacing System */
  --spacing-unit: 8px; /* Base spacing unit */
  --spacing-xs: calc(var(--spacing-unit) * 0.5); /* 4px */
  --spacing-sm: calc(var(--spacing-unit) * 1); /* 8px */
  --spacing-md: calc(var(--spacing-unit) * 2); /* 16px */
  --spacing-lg: calc(var(--spacing-unit) * 3); /* 24px */
  --spacing-xl: calc(var(--spacing-unit) * 4); /* 32px */
  --spacing-xxl: calc(var(--spacing-unit) * 6); /* 48px */

  /* Shadows */
  --box-shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.12);
  --box-shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.07);
  --box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.12), 0 4px 6px -2px rgba(0, 0, 0, 0.07);
  --box-shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.06);

  /* Transitions */
  --transition-speed: 0.2s;
  --transition-function: cubic-bezier(0.4, 0, 0.2, 1);

  /* Military-inspired patterns */
  --grid-pattern: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h20v20H0V0zm10 10h10v10H10V10zM0 10h10v10H0V10z' fill='%23000000' fill-opacity='0.03'/%3E%3C/svg%3E");
  --topo-pattern: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0c50 0 50 20 100 20V0H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 20c50 0 50 20 100 20V20H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 40c50 0 50 20 100 20V40H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 60c50 0 50 20 100 20V60H0z' fill='%23000000' fill-opacity='0.02'/%3E%3Cpath d='M0 80c50 0 50 20 100 20V80H0z' fill='%23000000' fill-opacity='0.02'/%3E%3C/svg%3E");

  /* Retro Mode Colors */
  --retro-bg: #000000;
  --retro-text: #00ff00;
  --retro-text-dim: #008800;
  --retro-friendly: #00aaff;
  --retro-enemy: #ff4444;
  --retro-border: #004400;
  --retro-panel-bg: #001100;
}

body {
  font-family: var(--font-family-ui);
  background-color: var(--background-light);
  background-image: var(--topo-pattern);
  color: var(--text-medium);
  margin: 0;
  padding: var(--spacing-xl); /* Enhanced 32px padding */
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: var(--font-weight-regular);
  letter-spacing: 0.01em;
  position: relative;
  min-height: 100vh;
}

.container {
  width: 95%;
  max-width: 1400px; /* Increased from 1200px for better use of widescreen displays */
  margin-bottom: var(--spacing-xl); /* Enhanced bottom spacing */
  position: relative;
  background-color: rgba(255, 255, 255, 0.92);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  padding: var(--spacing-xxl); /* Enhanced padding for better breathing room */
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xl); /* Enhanced 32px margin below headings */
  margin-top: var(--spacing-lg); /* Enhanced 24px margin above headings */
  position: relative;
  line-height: 1.3;
}

h1, h2 {
  text-align: center;
}

h1 {
  font-size: 2.5rem; /* Larger heading */
  margin-top: calc(var(--spacing-unit) * 2);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.01em;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
  border-radius: 2px;
}

h2 {
  font-size: 1.8rem;
  color: var(--primary-dark);
  padding-bottom: 8px;
  position: relative;
}

h2::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: var(--military-olive);
  border-radius: 2px;
  opacity: 0.8;
}

h3 {
  font-size: 1.4rem;
  color: var(--secondary-dark);
  padding-left: 12px;
  border-left: 3px solid var(--military-olive-light);
}

h4 {
  font-size: 1.2rem;
  color: var(--secondary-dark);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: 1.1rem;
  color: var(--secondary-color);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: 1rem;
  color: var(--text-medium);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.intro {
  text-align: center;
  color: var(--text-medium);
  margin-bottom: calc(var(--spacing-unit) * 4); /* 32px margin below intro */
  font-size: 1.1rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.7;
  padding: calc(var(--spacing-unit) * 3);
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--military-olive-light);
  box-shadow: var(--box-shadow-subtle);
  position: relative;
}

.intro::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--military-olive-light), transparent);
  opacity: 0.5;
}

/* --- Collapsible Section Styles --- */
.collapsible {
  background-color: var(--background-medium);
  color: var(--text-dark);
  cursor: pointer;
  padding: var(--spacing-lg); /* Enhanced 24px padding for better touch targets */
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-speed) var(--transition-function);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  box-shadow: var(--box-shadow-subtle);
  position: relative;
  overflow: hidden;
  border-left: 4px solid var(--military-olive);
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
}

.collapsible::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(to right, rgba(var(--military-olive-dark), 0.05), transparent);
  opacity: 0.7;
  transition: opacity var(--transition-speed) var(--transition-function);
}

.collapsible::after {
  content: '\002B'; /* Unicode plus sign */
  color: var(--military-olive-dark);
  font-weight: bold;
  float: right;
  margin-left: auto;
  font-size: 1.2rem;
  transition: transform var(--transition-speed) var(--transition-function);
  position: relative;
  z-index: 2;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.active::after {
  content: '\2212'; /* Unicode minus sign */
  transform: rotate(180deg);
  background-color: rgba(255, 255, 255, 0.3);
}

.collapsible:hover {
  background-color: var(--background-medium);
  box-shadow: var(--box-shadow-medium);
  transform: translateY(-2px);
  border-left-color: var(--military-olive-light);
}

.collapsible:hover::before {
  opacity: 0.9;
}

.collapsible.active {
  background-color: var(--military-olive);
  color: var(--text-light);
  margin-bottom: 0; /* Remove margin when active */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: var(--box-shadow-medium);
  border-left-color: var(--military-olive-dark);
}

.collapsible.active::before {
  opacity: 0.1;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), transparent);
}

.collapsible.active::after {
  color: var(--text-light);
}

.content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-speed) var(--transition-function), padding 0s var(--transition-speed);
  background-color: var(--surface-color);
  background-image: var(--grid-pattern);
  border: 1px solid var(--border-color);
  border-top: none;
  border-left: 4px solid var(--military-olive-dark);
  border-bottom-left-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-xl); /* Enhanced 32px margin for better section separation */
  box-shadow: var(--box-shadow-medium);
  visibility: hidden;
  opacity: 0;
  transition: max-height var(--transition-speed) var(--transition-function),
              opacity var(--transition-speed) var(--transition-function),
              visibility 0s var(--transition-speed);
  position: relative;
}

.content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

.content ol, .content ul {
  padding-left: calc(var(--spacing-unit) * 3); /* 24px padding */
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  position: relative;
}

.content li {
  margin-bottom: var(--spacing-unit); /* 8px margin */
  position: relative;
}

.content li::marker {
  color: var(--military-olive);
  font-weight: var(--font-weight-semibold);
}

/* --- Map and Controls Layout --- */
.map-controls-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxl); /* Enhanced 48px gap for better separation */
  margin-bottom: var(--spacing-xxl); /* Enhanced 48px margin */
}

/* --- Map Styles --- */
#map-container, #mortar-map-container {
  width: 100%;
}

#map, #mortar-map {
  height: 500px; /* Slightly increased map height */
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

#map::before, #mortar-map::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
  z-index: 10;
  opacity: 0.8;
}

/* Map Legend Styles */
#map-legend {
  position: relative;
  background-color: rgba(255, 255, 255, 0.92);
  background-image: var(--grid-pattern);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin-top: calc(var(--spacing-unit) * 1.5);
  box-shadow: var(--box-shadow-medium);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

#map-legend::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

#map-legend.collapsed {
  max-height: 45px;
  opacity: 0.9;
  cursor: pointer;
  border-left: 4px solid var(--military-olive-light);
}

#map-legend.expanded {
  max-height: 500px;
  opacity: 1;
}

#map-legend h4 {
  margin-top: 0;
  margin-bottom: calc(var(--spacing-unit) * 1.5);
  font-size: 1rem;
  color: var(--primary-color);
  text-align: center;
  font-weight: 600;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

#map-legend h4::after {
  content: '▼';
  font-size: 0.8rem;
  margin-left: 8px;
  transition: transform 0.3s ease;
  background-color: var(--military-olive-light);
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

#map-legend.collapsed h4::after {
  transform: rotate(-90deg);
  background-color: var(--military-olive);
}

#map-legend.collapsed h4 {
  margin-bottom: 0;
}

#map-legend.collapsed {
  background-color: rgba(255, 255, 255, 0.85);
}

#map-legend.collapsed:hover {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: var(--box-shadow-large);
  transform: translateY(-2px);
}

.legend-content {
  transition: opacity 0.3s ease-in-out;
  position: relative;
  z-index: 2;
}

#map-legend.collapsed .legend-content {
  opacity: 0;
  pointer-events: none;
}

#map-legend.expanded .legend-content {
  opacity: 1;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: calc(var(--spacing-unit) * 2);
  margin-bottom: calc(var(--spacing-unit) * 2);
}

.force-type-indicators {
  display: flex;
  justify-content: center;
  gap: calc(var(--spacing-unit) * 3);
  margin-top: calc(var(--spacing-unit) * 2);
  padding-top: calc(var(--spacing-unit) * 2);
  border-top: 1px solid var(--border-color);
  position: relative;
}

.force-type-indicators::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  width: 80px;
  height: 1px;
  background: linear-gradient(to right, var(--military-olive), transparent);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing-unit) * 1.25);
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.5);
  padding: calc(var(--spacing-unit) * 0.75) calc(var(--spacing-unit) * 1.25);
  border-radius: var(--border-radius-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.legend-item:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-subtle);
}

.legend-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Common military icon style */
.legend-military-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #777;
  background-color: rgba(119, 119, 119, 0.9);
  color: white;
  font-size: 7px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Common styles for legend icons */
.legend-icon {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
}

/* Infantry icon - Circle */
.legend-infantry {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
  border-radius: 50%;
}

/* Tank icon - Triangle */
.legend-tank {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-tank::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 20px solid #777;
  top: 0;
  left: 0;
}

/* MG icon - Square */
.legend-mg {
  width: 20px;
  height: 20px;
  border: 1px solid #555;
  background-color: #777;
}

/* Artillery icon - Diamond */
.legend-artillery {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-artillery::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #777;
  transform: rotate(45deg);
  top: 0;
  left: 0;
  border: 1px solid #555;
}

/* Anti-Aircraft icon - Diamond with cross */
.legend-antiair {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-antiair::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #777;
  transform: rotate(45deg);
  top: 0;
  left: 0;
  border: 1px solid #555;
}

.legend-antiair .cross-horizontal {
  position: absolute;
  top: 9.5px;
  left: 0;
  width: 20px;
  height: 1px;
  background-color: white;
  z-index: 1;
}

.legend-antiair .cross-vertical {
  position: absolute;
  top: 0;
  left: 9.5px;
  width: 1px;
  height: 20px;
  background-color: white;
  z-index: 1;
}

/* Reconnaissance icon - Hexagon */
.legend-recon {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-recon svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* APC icon - Rounded Rectangle */
.legend-apc {
  width: 24px;
  height: 16px;
  border: 1px solid #555;
  background-color: #777;
  border-radius: 5px;
}

/* Helicopter icon - Pentagon */
.legend-heli {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-heli svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* Command Post icon - Star */
.legend-command {
  position: relative;
  width: 20px;
  height: 20px;
}

.legend-command svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}

/* Force type indicators */
.legend-friendly {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: blue;
  border: 1px solid #000066;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 200, 0.4);
  position: relative;
}

.legend-friendly::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.legend-enemy {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: red;
  border: 1px solid #990000;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 4px rgba(200, 0, 0, 0.4);
  position: relative;
}

.legend-enemy::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

/* Leaflet Draw Control Styles */
.leaflet-draw-toolbar a {
  transition: all var(--transition-speed) var(--transition-function);
}

/* Grid Instructions Styles */
.grid-instructions {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: calc(var(--spacing-unit) * 2);
  margin-bottom: calc(var(--spacing-unit) * 2);
  box-shadow: var(--box-shadow-subtle);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.grid-instructions p {
  display: flex;
  align-items: center;
  margin: 0;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  font-size: 0.8rem;
  line-height: 1.4;
  letter-spacing: 0.01em;
}

/* Tool Icon Styles */
.tool-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  position: relative;
  flex-shrink: 0;
}

.rectangle-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23e65100" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>');
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background-color: rgba(230, 81, 0, 0.05);
}

.rectangle-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  background-color: rgba(230, 81, 0, 0.1);
}

/* Highlighted state for the rectangle draw tool */
.leaflet-draw-toolbar .leaflet-draw-draw-rectangle.highlight-tool {
  background-color: var(--action-color);
  box-shadow: 0 0 0 3px rgba(var(--action-color-rgb, 245, 158, 11), 0.3);
}

/* Pulse animation for the active draw tool */
@keyframes draw-tool-pulse {
  0% { box-shadow: 0 0 0 0 rgba(var(--action-color-rgb, 245, 158, 11), 0.7); }
  70% { box-shadow: 0 0 0 6px rgba(var(--action-color-rgb, 245, 158, 11), 0); }
  100% { box-shadow: 0 0 0 0 rgba(var(--action-color-rgb, 245, 158, 11), 0); }
}

/* === ENHANCED MAP CONTROLS === */

/* Enhanced Leaflet Controls */
.leaflet-control-zoom {
  margin-left: var(--spacing-md) !important; /* Enhanced 16px margin */
  margin-top: var(--spacing-md) !important; /* Enhanced 16px margin */
  box-shadow: var(--box-shadow-medium) !important;
  border-radius: var(--border-radius-md) !important;
  overflow: hidden;
}

.leaflet-control-zoom a {
  background-color: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-dark) !important;
  font-weight: var(--font-weight-bold) !important;
  transition: all 0.2s ease !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
}

.leaflet-control-zoom a:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-control-zoom a:active {
  transform: scale(0.95) !important;
}

/* Enhanced Draw Controls */
.leaflet-draw-toolbar {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: var(--border-radius-md) !important;
  box-shadow: var(--box-shadow-medium) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border: 1px solid var(--border-color) !important;
  overflow: hidden;
}

.leaflet-draw-toolbar a {
  border: none !important;
  background-color: transparent !important;
  transition: all 0.2s ease !important;
  position: relative;
}

.leaflet-draw-toolbar a:hover {
  background-color: var(--primary-color) !important;
  transform: scale(1.05) !important;
}

.leaflet-draw-toolbar a:active {
  transform: scale(0.95) !important;
}

.leaflet-draw-toolbar a.leaflet-draw-toolbar-button-enabled {
  background-color: var(--accent-color) !important;
  color: white !important;
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
}

/* Map Interaction Feedback */
.map-crosshair {
  cursor: crosshair !important;
}

.map-interaction-overlay {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--box-shadow-medium);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1000;
  font-family: var(--font-family-data);
  font-size: 0.85rem;
  color: var(--text-dark);
  pointer-events: none;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.map-interaction-overlay.show {
  opacity: 1;
  transform: translateY(0);
}

.coordinate-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.coordinate-label {
  font-size: 0.7rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-medium);
}

.coordinate-value {
  font-family: 'Courier New', monospace;
  font-weight: var(--font-weight-bold);
  color: var(--military-olive-dark);
  letter-spacing: 0.5px;
}

/* Enhanced Map Tooltips */
.map-tooltip {
  background: rgba(0, 0, 0, 0.85) !important;
  border: none !important;
  border-radius: var(--border-radius) !important;
  color: white !important;
  font-size: 0.8rem !important;
  font-weight: var(--font-weight-medium) !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  box-shadow: var(--box-shadow-medium) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
}

.map-tooltip::before {
  border-top-color: rgba(0, 0, 0, 0.85) !important;
}

/* --- Help Icon & Tooltip System --- */

/* Help Icon Styling */
.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--info-color);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
  cursor: help;
  transition: all var(--transition-speed) var(--transition-function);
  position: relative;
  vertical-align: baseline;
  -webkit-user-select: none;
  user-select: none;
  border: 2px solid transparent;
  line-height: 20px;
  text-align: center;
  font-family: Arial, sans-serif;
  padding: 0;
  box-sizing: border-box;
}

.help-icon:hover,
.help-icon:focus {
  background: var(--info-dark);
  transform: scale(1.1);
  border-color: var(--info-light);
  outline: none;
}

.help-icon:active {
  transform: scale(0.95);
}

/* Tooltip Container */
.tooltip-container {
  position: relative;
  display: inline-block;
}

/* Base Tooltip Styles */
.tooltip {
  position: absolute;
  z-index: 1000;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  max-width: 250px;
  word-wrap: break-word;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) var(--transition-function);
  pointer-events: none;
  box-shadow: var(--box-shadow-large);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* Show tooltip on hover and focus */
.tooltip-container:hover .tooltip,
.tooltip-container:focus .tooltip,
.tooltip-container:focus-within .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Tooltip Positioning */
.tooltip-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  margin-bottom: 8px;
}

.tooltip-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(8px);
  margin-top: 8px;
}

.tooltip-left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(-8px);
  margin-right: 8px;
}

.tooltip-right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(8px);
  margin-left: 8px;
}

/* Tooltip show states with proper transforms */
.tooltip-container:hover .tooltip-top,
.tooltip-container:focus .tooltip-top,
.tooltip-container:focus-within .tooltip-top {
  transform: translateX(-50%) translateY(0);
}

.tooltip-container:hover .tooltip-bottom,
.tooltip-container:focus .tooltip-bottom,
.tooltip-container:focus-within .tooltip-bottom {
  transform: translateX(-50%) translateY(0);
}

.tooltip-container:hover .tooltip-left,
.tooltip-container:focus .tooltip-left,
.tooltip-container:focus-within .tooltip-left {
  transform: translateY(-50%) translateX(0);
}

.tooltip-container:hover .tooltip-right,
.tooltip-container:focus .tooltip-right,
.tooltip-container:focus-within .tooltip-right {
  transform: translateY(-50%) translateX(0);
}

/* Tooltip Arrows */
.tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.tooltip-top::before {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: inherit;
}

.tooltip-bottom::before {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: inherit;
}

.tooltip-left::before {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: inherit;
}

.tooltip-right::before {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: inherit;
}

/* Tooltip Type Styles */
.tooltip-default {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-default::before {
  border-top-color: rgba(0, 0, 0, 0.9);
  border-bottom-color: rgba(0, 0, 0, 0.9);
  border-left-color: rgba(0, 0, 0, 0.9);
  border-right-color: rgba(0, 0, 0, 0.9);
}

.tooltip-military {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
  color: white;
  border: 1px solid var(--accent-light);
  font-weight: var(--font-weight-semibold);
}

.tooltip-military::before {
  border-top-color: var(--accent-color);
  border-bottom-color: var(--accent-color);
  border-left-color: var(--accent-color);
  border-right-color: var(--accent-color);
}

.tooltip-technical {
  background: linear-gradient(135deg, var(--info-color), var(--info-dark));
  color: white;
  border: 1px solid var(--info-light);
  font-family: var(--font-family-mono);
  font-size: 0.75rem;
}

.tooltip-technical::before {
  border-top-color: var(--info-color);
  border-bottom-color: var(--info-color);
  border-left-color: var(--info-color);
  border-right-color: var(--info-color);
}

/* Enhanced tooltip animations */
.tooltip-container:hover .tooltip {
  animation: tooltipFadeIn 0.2s ease-out forwards;
}

@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* --- Enhanced Visual Feedback Improvements --- */

/* --- Enhanced Accessibility Improvements --- */

/* Enhanced Button Focus States */
button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(29, 53, 87, 0.15);
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: var(--spacing-sm);
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  z-index: 9999;
}

/* Enhanced Focus Indicators for All Interactive Elements */
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
button:focus-visible,
a:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(29, 53, 87, 0.15);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --text-dark: #000000;
    --text-medium: #000000;
    --text-light: #ffffff;
    --background-color: #ffffff;
    --surface-color: #ffffff;
    --border-color: #000000;
    --primary-color: #000080;
    --accent-color: #008000;
    --danger-color: #800000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Enhanced Keyboard Navigation */
.keyboard-navigation-active *:focus {
  outline: 3px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  border-radius: var(--border-radius);
  z-index: 10000;
  font-weight: var(--font-weight-semibold);
  transition: top var(--transition-speed) var(--transition-function);
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced Touch Targets for Mobile */
@media (pointer: coarse) {
  button,
  input,
  select,
  textarea,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  .help-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }
}

/* Large Touch Mode */
body.large-touch-mode button,
body.large-touch-mode input,
body.large-touch-mode select,
body.large-touch-mode textarea {
  min-height: 56px;
  font-size: 1.1rem;
  padding: var(--spacing-md);
}

body.large-touch-mode .help-icon {
  width: 28px;
  height: 28px;
  font-size: 16px;
  line-height: 1;
}

/* High Contrast Mode */
body.high-contrast-mode {
  --text-dark: #000000;
  --text-medium: #000000;
  --text-light: #ffffff;
  --background-color: #ffffff;
  --surface-color: #ffffff;
  --border-color: #000000;
  --primary-color: #000080;
  --accent-color: #008000;
  --danger-color: #800000;
}

body.high-contrast-mode button {
  border: 2px solid currentColor;
}

body.high-contrast-mode input,
body.high-contrast-mode select,
body.high-contrast-mode textarea {
  border: 2px solid #000000;
  background: #ffffff;
  color: #000000;
}

/* Night Vision Mode */
body.night-vision-mode {
  --background-color: #1a1a1a;
  --surface-color: #2d2d2d;
  --text-dark: #ffffff;
  --text-medium: #e0e0e0;
  --text-light: #ffffff;
  --border-color: #404040;
  --primary-color: #4a9eff;
  --accent-color: #4ade80;
  --danger-color: #ef4444;
}

/* Focus Management for Modal Dialogs */
.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Improved Error States for Accessibility */
.field-invalid {
  border-color: var(--danger-color) !important;
  background-color: rgba(239, 68, 68, 0.05);
}

.field-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
}

.field-valid {
  border-color: var(--accent-color) !important;
  background-color: rgba(74, 222, 128, 0.05);
}

.field-valid:focus {
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.15);
}

/* Enhanced Button Press Feedback */
button:active:not(:disabled) {
  transform: translateY(1px) scale(0.98);
  transition: transform 0.1s ease-out;
}

/* Ripple Effect for Button Clicks */
.button-ripple {
  position: relative;
  overflow: hidden;
}

.button-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease-out, height 0.3s ease-out;
  pointer-events: none;
}

.button-ripple:active::before {
  width: 200px;
  height: 200px;
}

/* Enhanced Loading Button States */
button.loading {
  pointer-events: none;
  opacity: 0.7;
}

button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: buttonSpinner 0.8s linear infinite;
}

@keyframes buttonSpinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success/Error Button States */
button.success {
  background-color: var(--accent-color) !important;
  animation: successPulse 0.6s ease-out;
}

button.error {
  background-color: var(--danger-color) !important;
  animation: errorShake 0.5s ease-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(39, 174, 96, 0.4); }
  100% { transform: scale(1); }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Enhanced Form Field Focus */
input:focus, select:focus, textarea:focus {
  transform: translateY(-1px);
  transition: all var(--transition-speed) var(--transition-function);
}

/* Improved Disabled States */
button:disabled, input:disabled, select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(0.3);
}

/* Enhanced Hover Feedback for Interactive Elements */
.interactive-element {
  transition: all var(--transition-speed) var(--transition-function);
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

/* Loading States and Progress Indicators */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

.loading-text {
  color: var(--text-dark);
  font-weight: var(--font-weight-medium);
  font-size: 0.9rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Form Validation Feedback */
.form-field-valid {
  border-left-color: var(--accent-color) !important;
  box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.15) !important;
}

.form-field-invalid {
  border-left-color: var(--danger-color) !important;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.15) !important;
}

.validation-message {
  font-size: 0.75rem;
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.validation-message.success {
  background-color: rgba(39, 174, 96, 0.1);
  color: var(--accent-dark);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

.validation-message.error {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--danger-dark);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

/* Enhanced Button States */
button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

button.loading {
  position: relative;
  color: transparent !important;
}

button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* --- Controls Container Styles --- */
#controls-container, #mortar-controls-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 4); /* 32px gap */
  background-color: var(--surface-color); /* Use surface color */
  background-image: var(--grid-pattern);
  padding: calc(var(--spacing-unit) * 4.5); /* 36px padding */
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  width: 100%; /* Ensure it takes full width of container */
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

#controls-container::before, #mortar-controls-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--military-olive), transparent);
  opacity: 0.7;
}

section {
  padding-bottom: var(--spacing-xxl); /* Enhanced 48px padding bottom for better separation */
  border-bottom: 1px solid var(--border-color);
  position: relative;
  margin-bottom: var(--spacing-xl); /* Enhanced 32px margin bottom */
}

section:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

section::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 2px;
  background: linear-gradient(to right, var(--military-olive), transparent);
  opacity: 0.8;
}

section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 20px;
  background: var(--military-olive-light);
  opacity: 0.6;
  border-radius: 0 2px 2px 0;
}

section:last-child::after {
  display: none;
}


.form-group {
  margin-bottom: var(--spacing-xl); /* Enhanced 32px margin for better separation */
  position: relative;
}

.form-group::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--military-olive-light), transparent);
  opacity: 0.5;
}

label {
  display: block;
  margin-bottom: var(--spacing-md); /* Enhanced 16px margin for better spacing */
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  font-size: 1rem;
  letter-spacing: 0.01em;
  position: relative;
  padding-left: var(--spacing-md); /* Enhanced 16px left padding */
}

label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 14px;
  background: var(--military-olive-light);
  opacity: 0.6;
  border-radius: 2px;
}

select,
input[type="number"],
input[type="text"] {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-md); /* Enhanced 24px 16px padding for better touch targets */
  border: 1px solid var(--border-color);
  border-left: 3px solid var(--military-olive);
  border-radius: var(--border-radius);
  box-sizing: border-box;
  font-size: 1rem;
  color: var(--text-dark);
  background-color: var(--background-light);
  transition: all var(--transition-speed) var(--transition-function);
  box-shadow: var(--box-shadow-subtle);
  font-family: var(--font-family-ui);
  position: relative;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%234b5320' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 35px;
}

select:hover,
input[type="number"]:hover,
input[type="text"]:hover {
  border-color: var(--border-color-hover);
  border-left: 3px solid var(--military-olive-light);
  box-shadow: var(--box-shadow-medium);
  transform: translateY(-1px);
}

select:focus,
input[type="number"]:focus,
input[type="text"]:focus {
  outline: none;
  border-color: var(--primary-light);
  border-left: 3px solid var(--primary-color);
  box-shadow: 0 0 0 3px rgba(29, 53, 87, 0.15); /* Subtle focus ring */
  background-color: var(--surface-color);
}

button {
  /* Base button styles - secondary by default */
  color: var(--text-light);
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3); /* 12px 24px padding */
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-speed) var(--transition-function);
  background-color: var(--secondary-color); /* Secondary by default */
  box-shadow: var(--box-shadow-subtle);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.03em;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity var(--transition-speed) var(--transition-function);
}

button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.2);
  opacity: 1;
  transition: all var(--transition-speed) var(--transition-function);
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-subtle);
}

button:hover:not(:disabled)::before {
  opacity: 1;
}

button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: var(--box-shadow-subtle);
  animation: none; /* Remove glow on click, will add via JS class */
}

button:active:not(:disabled)::after {
  height: 0;
  opacity: 0;
}

/* === BUTTON HIERARCHY === */

/* Primary Action Buttons - Most important actions */
.btn-primary,
#add-target-button,
#send-targets-button,
#calculate-solution-button {
  background-color: var(--primary-color);
  font-size: 1rem; /* Slightly larger than default */
  font-weight: 600; /* Bolder */
  padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 4); /* More padding */
  box-shadow: var(--box-shadow-medium); /* More prominent shadow */
  border: 2px solid transparent;
  position: relative;
}

/* Subtle glow for primary buttons when enabled */
.btn-primary:not(:disabled),
#add-target-button:not(:disabled),
#send-targets-button:not(:disabled),
#calculate-solution-button:not(:disabled) {
  box-shadow: var(--box-shadow-medium), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.btn-primary:hover:not(:disabled),
#add-target-button:hover:not(:disabled),
#send-targets-button:hover:not(:disabled),
#calculate-solution-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-2px); /* More lift on hover */
  box-shadow: var(--box-shadow-large);
}

/* Specific Primary Button Colors */
#add-target-button {
  background-color: var(--accent-color); /* Green for add */
}
#add-target-button:hover:not(:disabled) {
   background-color: var(--accent-dark); /* Darker green on hover */
}

/* Enhanced Target List Button Styles */
.target-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Legacy support */
.button-group {
  display: flex;
  gap: var(--spacing-md); /* Enhanced 16px gap between buttons */
  justify-content: flex-end;
  margin-top: var(--spacing-lg); /* Enhanced 24px top margin */
}

.button-group button {
  padding: var(--spacing-sm) var(--spacing-md); /* Enhanced 8px 16px padding */
  font-size: 0.85em;
  border-radius: calc(var(--border-radius) * 0.75); /* Slightly smaller radius */
  min-width: 80px; /* Enhanced minimum width for better touch targets */
  text-align: center;
  font-weight: var(--font-weight-semibold);
}

.go-to-button {
  background-color: var(--info-color); /* Blue for info/go to */
}

.go-to-button:hover:not(:disabled) {
  background-color: var(--info-dark); /* Darker blue on hover */
}

.go-to-button:active:not(:disabled) {
  animation: glow-info 0.5s ease-in-out infinite alternate;
}

.remove-button {
  background-color: var(--danger-color); /* Red for remove */
}

.remove-button:hover:not(:disabled) {
  background-color: var(--danger-dark); /* Darker red on hover */
}

.remove-button:active:not(:disabled) {
  animation: glow-danger 0.5s ease-in-out infinite alternate;
}

@keyframes glow-info {
  from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(52, 152, 219, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(52, 152, 219, 0.8); }
}


/* Secondary Action Buttons - Supporting actions */
.btn-secondary,
#refresh-targets-button {
  background-color: var(--info-color);
  font-size: 0.95rem;
}

.btn-secondary:hover:not(:disabled),
#refresh-targets-button:hover:not(:disabled) {
  background-color: var(--info-dark);
}

#send-targets-button {
  background-color: var(--action-color); /* Orange for critical send action */
}
#send-targets-button:hover:not(:disabled) {
  background-color: var(--action-dark); /* Darker orange on hover */
}
#send-targets-button:disabled {
  background-color: #a0cfff; /* Lighter blue when disabled */
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}


/* Tertiary/Utility Buttons - Less prominent actions */
.btn-tertiary,
#retro-mode-toggle {
  background-color: var(--secondary-color);
  font-size: 0.9rem;
  padding: calc(var(--spacing-unit) * 1.25) calc(var(--spacing-unit) * 2.5);
}

.btn-tertiary:hover:not(:disabled),
#retro-mode-toggle:hover:not(:disabled) {
  background-color: var(--secondary-dark);
}

#refresh-targets-button:disabled {
   background-color: #a0cfff; /* Lighter blue when disabled */
   cursor: not-allowed;
   box-shadow: none;
   transform: none;
}

#calculate-solution-button {
  background-color: var(--accent-color); /* Emerald green for calculate */
}
#calculate-solution-button:hover:not(:disabled) {
   background-color: var(--accent-dark); /* Darker green on hover */
}
#calculate-solution-button:disabled {
  background-color: #d4edda; /* Lighter green when disabled */
  color: #155724;
  cursor: not-allowed;
  border-color: #c3e6cb;
  box-shadow: none;
  transform: none;
}


/* Danger/Warning Buttons - Destructive actions */
.btn-danger,
#clear-targets-button,
#clear-targets-button-mortar,
#clear-grid-lines-button {
  background-color: var(--danger-color); /* Red for clear */
  margin-top: calc(var(--spacing-unit) * 3); /* 24px top margin */
  width: 100%; /* Make button full width */
  margin-bottom: calc(var(--spacing-unit) * 1.5); /* Add bottom margin */
  font-size: 0.95rem; /* Slightly smaller font */
  border-left: 3px solid var(--danger-dark); /* Left border for visual distinction */
  font-weight: 500; /* Less bold than primary actions */
}

#retro-mode-toggle {
  background-color: var(--secondary-color); /* Gray for theme toggle */
  margin-top: calc(var(--spacing-unit) * 1.5); /* 12px top margin */
  width: 100%; /* Make button full width */
  font-size: 0.95rem; /* Slightly smaller font */
  border-left: 3px solid var(--secondary-dark); /* Left border for visual distinction */
}

.btn-danger:hover:not(:disabled),
#clear-targets-button:hover:not(:disabled),
#clear-targets-button-mortar:hover:not(:disabled),
#clear-grid-lines-button:hover:not(:disabled) {
  background-color: var(--danger-dark); /* Darker red on hover */
  border-left-color: var(--danger-color); /* Lighter border on hover */
}

#retro-mode-toggle:hover:not(:disabled) {
  background-color: var(--secondary-dark); /* Darker gray on hover */
  border-left-color: var(--secondary-color); /* Lighter border on hover */
}
#clear-grid-lines-button:active:not(:disabled) {
  animation: glow-danger 0.5s ease-in-out infinite alternate;
}

/* Draw grid button styles removed - feature not implemented */


/* Glow Animation (Applied via JS class on Active) */
.button-glow {
   animation: glow 0.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
      box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(var(--accent-color, 44, 62, 80), 0.5);
  }
  to {
      box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(var(--accent-color, 44, 62, 80), 0.8);
  }
}

/* Specific Glows (optional, for different colors) */
.glow-green {
  animation-name: glow-green;
}
@keyframes glow-green {
  from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(39, 174, 96, 0.5); } /* Using RGB values for variables in rgba */
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(39, 174, 96, 0.8); }
}

.glow-primary {
  animation-name: glow-primary;
}
@keyframes glow-primary {
   from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(44, 62, 80, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(44, 62, 80, 0.8); }
}

.glow-danger {
  animation-name: glow-danger;
}
@keyframes glow-danger {
   from { box-shadow: 0 0 calc(var(--spacing-unit) * 0.5) rgba(231, 76, 60, 0.5); }
  to { box-shadow: 0 0 calc(var(--spacing-unit) * 2) rgba(231, 76, 60, 0.8); }
}

.glow-info { /* Specific glow for info color */
  animation-name: glow-info;
}

/* --- Table Styles --- */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
  font-size: 0.95rem;
  box-shadow: var(--box-shadow-medium);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
  background-color: var(--surface-color);
  position: relative;
}

table::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--military-olive);
  z-index: 1;
}

th {
  background-color: var(--primary-color);
  color: var(--text-light);
  font-weight: var(--font-weight-semibold);
  text-align: left;
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2); /* 12px 16px padding */
  border-bottom: 2px solid var(--primary-dark);
  position: relative;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(0, 0, 0, 0.05));
}

th:first-child {
  padding-left: calc(var(--spacing-unit) * 3);
}

td {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2); /* 12px 16px padding */
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
  transition: background-color var(--transition-speed) var(--transition-function);
}

td:first-child {
  padding-left: calc(var(--spacing-unit) * 3);
  position: relative;
}

tr:last-child td {
  border-bottom: none;
}

tr:nth-child(even) td {
  background-color: var(--background-light);
}

tr:hover td {
  background-color: var(--background-medium);
}

/* --- Mortar-specific Styles --- */

/* Mortar Position Display */
#current-mortar-pos {
  padding: calc(var(--spacing-unit) * 1.5); /* 12px padding */
  background-color: var(--background-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family-data); /* Use data font for coordinates */
  font-size: 1em;
  text-align: center;
  color: var(--text-dark);
}

/* Weapon Selection Layout */
.weapon-selection {
  display: flex;
  flex-wrap: wrap;
  gap: calc(var(--spacing-unit) * 3); /* 24px gap */
  align-items: flex-start;
}

/* Calculation Solution Display */
#calculated-solution-display {
  margin-top: calc(var(--spacing-unit) * 3); /* 24px margin */
  padding: calc(var(--spacing-unit) * 3); /* 24px padding */
  background-color: var(--background-light);
  background-image: var(--grid-pattern);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive);
  border-radius: var(--border-radius);
  text-align: left;
  min-height: 120px;
  font-size: 1em;
  line-height: 1.6;
  color: var(--text-dark);
  box-shadow: var(--box-shadow-medium);
  position: relative;
  transition: all var(--transition-speed) ease;
  font-family: var(--font-family-data); /* Use data font for solution display */
  overflow: hidden;
}

#calculated-solution-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
  pointer-events: none;
  opacity: 0.5;
}

#calculated-solution-display h3 {
  margin-top: 0;
  margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
  text-align: center;
  color: var(--primary-color);
  font-size: 1.3em;
  font-weight: 600;
}

#calculated-solution-display p {
  margin: var(--spacing-unit) 0; /* 8px margin */
}

#calculated-solution-display hr {
  margin: calc(var(--spacing-unit) * 2.5) 0; /* 20px margin */
  border: 0;
  border-top: 1px solid #ccc;
}

#calculated-solution-display.calculated {
  background-color: #d4edda; /* Light green */
  border-color: #c3e6cb; /* Lighter green */
}

#calculated-solution-display .calculation-confirmation {
  position: absolute;
  top: var(--spacing-unit); /* 8px from top */
  right: var(--spacing-unit); /* 8px from right */
  font-size: 0.8em;
  color: green;
  font-weight: bold;
}

/* Target Display Area */
#target-display-area {
  width: 100%; /* Ensure it takes full width of container */
  box-sizing: border-box;
}

.target-list-controls {
  text-align: center;
  margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
}

#received-targets-list, #target-list-items {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: 350px;
  overflow-y: auto;
}

/* Enhanced Target List Item Styling */
#target-list-items li {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--military-olive); /* Slightly thicker left border */
  border-radius: var(--border-radius-md); /* Slightly larger radius */
  padding: var(--spacing-lg); /* Enhanced 24px padding for better content spacing */
  margin-bottom: var(--spacing-lg); /* Enhanced 24px margin between items */
  box-shadow: var(--box-shadow-subtle);
  transition: all 0.3s ease; /* Slightly longer transition for smoother feel */
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); /* 16px gap between content sections */
}

#target-list-items li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

/* Enhanced Target Card States */
#target-list-items li:hover {
  box-shadow: var(--box-shadow-large);
  border-color: var(--border-color-hover);
  border-left-color: var(--military-olive-light);
  transform: translateY(-3px);
  background-color: rgba(255, 255, 255, 0.8);
}

#target-list-items li.selected {
  border-left-color: var(--primary-color);
  border-left-width: 5px;
  background-color: rgba(29, 53, 87, 0.05);
  box-shadow: var(--box-shadow-medium);
}

#target-list-items li.selected:hover {
  border-left-color: var(--primary-dark);
  background-color: rgba(29, 53, 87, 0.08);
}

/* Mortar Screen - Received Targets Selection Styles */
#received-targets-list li.selected {
  border-left-color: var(--primary-color) !important;
  border-left-width: 5px !important;
  background-color: rgba(29, 53, 87, 0.1) !important;
  box-shadow: var(--box-shadow-medium) !important;
  cursor: pointer;
  position: relative;
}

#received-targets-list li.selected::before {
  content: "🎯 SELECTED";
  position: absolute;
  top: 5px;
  right: 10px;
  font-size: 0.7rem;
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  background: rgba(29, 53, 87, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

#received-targets-list li.selected:hover {
  border-left-color: var(--primary-dark) !important;
  background-color: rgba(29, 53, 87, 0.15) !important;
}

/* Make received targets clickable */
#received-targets-list li {
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  position: relative;
  padding: var(--spacing-md);
  border-left: 3px solid transparent;
}

#received-targets-list li:hover {
  background-color: rgba(29, 53, 87, 0.05);
  border-left-color: var(--primary-light);
  transform: translateX(2px);
}

#received-targets-list li:active {
  transform: translateX(1px) scale(0.98);
}

#target-list-items li:last-child {
  margin-bottom: 0;
}

#target-list-items li.new-target {
  animation: highlight-new 1s ease-out;
}

@keyframes highlight-new {
  0% {
    background-color: rgba(39, 174, 96, 0.3);
    transform: scale(1.02);
    box-shadow: var(--box-shadow-large);
  }
  50% {
    background-color: rgba(39, 174, 96, 0.15);
  }
  100% {
    background-color: var(--surface-color);
    transform: scale(1);
    box-shadow: var(--box-shadow-subtle);
  }
}

/* === ENHANCED STATUS INDICATORS === */

/* System Status Bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--surface-color) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-subtle);
  position: relative;
  overflow: hidden;
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--primary-color), var(--info-color));
  opacity: 0.8;
}

.status-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.status-dot.connected {
  background-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.3);
  animation: pulse-connected 2s infinite;
}

.status-dot.connecting {
  background-color: var(--action-color);
  animation: pulse-connecting 1s infinite;
}

.status-dot.disconnected {
  background-color: var(--danger-color);
  animation: pulse-disconnected 1.5s infinite;
}

@keyframes pulse-connected {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes pulse-connecting {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes pulse-disconnected {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.3); }
}

.status-text {
  color: var(--text-dark);
  font-size: 0.8rem;
}

.status-count {
  background-color: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-family: var(--font-family-data);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  min-width: 24px;
  text-align: center;
}

/* Operation Feedback Indicators */
.operation-feedback {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  pointer-events: none;
}

.feedback-message {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--box-shadow-large);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  max-width: 300px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.feedback-message.show {
  transform: translateX(0);
}

.feedback-message.success {
  border-left: 4px solid var(--accent-color);
  color: var(--accent-dark);
}

.feedback-message.error {
  border-left: 4px solid var(--danger-color);
  color: var(--danger-dark);
}

.feedback-message.info {
  border-left: 4px solid var(--info-color);
  color: var(--info-dark);
}

.feedback-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.feedback-icon.success {
  background-color: var(--accent-color);
}

.feedback-icon.error {
  background-color: var(--danger-color);
}

.feedback-icon.info {
  background-color: var(--info-color);
}

/* Enhanced Target Card Structure */
.target-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.target-id-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.target-force-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  justify-content: center;
}

.target-force-badge.enemy {
  background-color: rgba(231, 76, 60, 0.15);
  color: var(--danger-dark);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.target-force-badge.friendly {
  background-color: rgba(52, 152, 219, 0.15);
  color: var(--info-dark);
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.target-type-label {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  text-transform: capitalize;
}

/* Priority and Status Indicators */
.target-priority-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.7rem;
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.target-priority-indicator.high {
  background-color: rgba(231, 76, 60, 0.15);
  color: var(--danger-dark);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.target-priority-indicator.medium {
  background-color: rgba(245, 158, 11, 0.15);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.target-priority-indicator.low {
  background-color: rgba(107, 114, 128, 0.15);
  color: var(--text-medium);
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.target-status-indicator {
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  background-color: rgba(39, 174, 96, 0.15);
  color: var(--accent-dark);
  border: 1px solid rgba(39, 174, 96, 0.3);
}

/* Legacy support for existing force indicators */
.target-force-indicator {
  font-family: var(--font-family-data);
  font-weight: var(--font-weight-semibold);
  font-size: 0.85em;
  letter-spacing: 0.5px;
  color: var(--text-medium);
}

/* Target Details Section */
.target-details {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-md);
  margin: var(--spacing-sm) 0;
  align-items: start;
}

.target-detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 0; /* Allow text to wrap */
}

/* Special handling for location item */
.target-location {
  grid-column: 1 / -1; /* Span full width */
  margin-top: var(--spacing-xs);
}

.location-text {
  word-break: break-word;
  overflow-wrap: break-word;
  font-family: var(--font-family-data);
  font-size: 0.9rem;
  line-height: 1.3;
  max-width: 100%;
}

.target-detail-icon {
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.target-detail-label {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.target-detail-value {
  font-family: var(--font-family-data);
  font-size: 0.9rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
}

.target-coordinates {
  grid-column: 1 / -1; /* Span full width */
  background-color: rgba(0, 0, 0, 0.03);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--military-olive-light);
}

.target-coordinates .target-detail-value {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: var(--military-olive-dark);
  letter-spacing: 0.5px;
}

/* Legacy support */
.target-detail-icon {
  font-family: var(--font-family-data);
  font-weight: var(--font-weight-bold);
  color: var(--text-muted);
  margin-right: calc(var(--spacing-unit) * 0.5);
}

/* Marker Styles */
.target-marker {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease-in-out; /* Smooth transition for marker effects */
  z-index: 900; /* Ensure markers appear above map elements but below mortar */
}

.target-marker.selected {
  transform: scale(1.2); /* Slightly enlarge selected marker */
  z-index: 950; /* Selected markers appear above other markers */
}

/* Enemy marker glow animation */
@keyframes enemy-marker-glow {
  0% { box-shadow: 0 0 0 0 rgba(200, 0, 0, 0.7); }
  50% { box-shadow: 0 0 4px 2px rgba(200, 0, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(200, 0, 0, 0.7); }
}

/* Friendly marker glow animation */
@keyframes friendly-marker-glow {
  0% { box-shadow: 0 0 0 0 rgba(0, 0, 200, 0.7); }
  50% { box-shadow: 0 0 4px 2px rgba(0, 0, 200, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(0, 0, 200, 0.7); }
}

.target-marker.target-enemy div {
  animation: enemy-marker-glow 2s infinite;
  box-sizing: border-box;
  position: relative;
}

.target-marker.target-friendly div {
  animation: friendly-marker-glow 2s infinite;
  box-sizing: border-box;
  position: relative;
}

/* Add inner details for better visibility */
.target-marker.target-enemy div::after,
.target-marker.target-friendly div::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 3px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.8;
}

/* Mortar Position Marker Styles */
.mortar-position-marker {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

/* Mortar glow animation */
@keyframes mortar-glow {
  0% { box-shadow: 0 0 0 0 rgba(0, 100, 0, 0.7); }
  50% { box-shadow: 0 0 5px 2px rgba(0, 100, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(0, 100, 0, 0.7); }
}

.mortar-position-marker div {
  animation: mortar-glow 2s infinite;
  z-index: 1000; /* Ensure it appears above other map elements */
  box-sizing: border-box;
  position: relative;
}

/* Add a small inner dot for better visibility */
.mortar-position-marker div::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  font-size: 1.2em;
  color: var(--primary-color);
}

.loading-overlay::after {
  content: '';
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Theme settings removed - using retro mode toggle instead */

/* Floating theme toggle removed - using retro mode button instead */

/* --- Screen Navigation Styles --- */
.screen-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--spacing-unit) * 4);
  padding: calc(var(--spacing-unit) * 3);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-large);
  color: white;
}

.screen-navigation h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-buttons {
  display: flex;
  gap: calc(var(--spacing-unit) * 2);
}

.nav-button {
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-function);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.nav-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.nav-button.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border-color: white;
  font-weight: 700;
}

.nav-button:disabled {
  cursor: not-allowed;
  opacity: 1;
}

/* Responsive navigation */
@media (max-width: 768px) {
  .screen-navigation {
    flex-direction: column;
    gap: calc(var(--spacing-unit) * 2);
    text-align: center;
  }

  .screen-navigation h1 {
    font-size: 1.5rem;
  }

  .nav-buttons {
    width: 100%;
    justify-content: center;
  }

  .nav-button {
    flex: 1;
    max-width: 150px;
  }
}

/* ===== RETRO MODE STYLES ===== */
body.retro-mode {
  background-color: var(--retro-bg) !important;
  background-image: none !important;
  color: var(--retro-text) !important;
  font-family: 'Courier New', monospace !important;
}

body.retro-mode .container,
body.retro-mode .control-panel,
body.retro-mode .map-controls-wrapper,
body.retro-mode section {
  background-color: var(--retro-panel-bg) !important;
  border: 1px solid var(--retro-border) !important;
  color: var(--retro-text) !important;
}

body.retro-mode h1,
body.retro-mode h2,
body.retro-mode h3,
body.retro-mode h4 {
  color: var(--retro-text) !important;
  text-shadow: 0 0 5px var(--retro-text);
}

body.retro-mode button {
  background-color: var(--retro-panel-bg) !important;
  border: 1px solid var(--retro-border) !important;
  color: var(--retro-text) !important;
  box-shadow: none !important;
}

body.retro-mode button:hover:not(:disabled) {
  background-color: var(--retro-border) !important;
  box-shadow: 0 0 10px var(--retro-text) !important;
  transform: none !important;
}

body.retro-mode input,
body.retro-mode select {
  background-color: var(--retro-bg) !important;
  border: 1px solid var(--retro-border) !important;
  color: var(--retro-text) !important;
}

body.retro-mode .target-force-indicator {
  color: var(--retro-text) !important;
}

/* Retro mode target colors */
body.retro-mode .target-marker.target-friendly {
  color: var(--retro-friendly) !important;
}

body.retro-mode .target-marker.target-enemy {
  color: var(--retro-enemy) !important;
}

/* Retro mode force indicators - using attribute selectors since :contains() isn't supported */
body.retro-mode .target-force-indicator {
  color: var(--retro-text) !important;
}

body.retro-mode #target-list-items li {
  background-color: var(--retro-panel-bg) !important;
  border: 1px solid var(--retro-border) !important;
  border-left: 3px solid var(--retro-text) !important;
}

body.retro-mode .screen-navigation {
  background: linear-gradient(135deg, var(--retro-bg) 0%, var(--retro-panel-bg) 100%) !important;
}

body.retro-mode .nav-button {
  background: var(--retro-panel-bg) !important;
  border: 1px solid var(--retro-border) !important;
  color: var(--retro-text) !important;
}

body.retro-mode .nav-button.active {
  background: var(--retro-border) !important;
  color: var(--retro-text) !important;
  box-shadow: 0 0 10px var(--retro-text) !important;
}

/* Fix all white backgrounds in retro mode */
body.retro-mode .container {
  background-color: var(--retro-panel-bg) !important;
  background-image: none !important;
}

body.retro-mode .content {
  background-color: var(--retro-bg) !important;
  background-image: none !important;
}

body.retro-mode .collapsible {
  background-image: none !important;
}

body.retro-mode .collapsible.active::before {
  background: none !important;
}

body.retro-mode #map-legend {
  background-color: var(--retro-panel-bg) !important;
  background-image: none !important;
}

body.retro-mode #map-legend.collapsed {
  background-color: var(--retro-panel-bg) !important;
}

body.retro-mode #map-legend.collapsed:hover {
  background-color: var(--retro-border) !important;
}

body.retro-mode .legend-item {
  background-color: var(--retro-bg) !important;
}

body.retro-mode .legend-item:hover {
  background-color: var(--retro-border) !important;
}

body.retro-mode .leaflet-control-zoom a {
  background-color: var(--retro-panel-bg) !important;
  color: var(--retro-text) !important;
  border-color: var(--retro-border) !important;
}

body.retro-mode .leaflet-draw-toolbar {
  background: var(--retro-panel-bg) !important;
}

body.retro-mode .grid-coordinates {
  background: var(--retro-panel-bg) !important;
  color: var(--retro-text) !important;
  border-color: var(--retro-border) !important;
}

body.retro-mode .feedback-message {
  background: var(--retro-panel-bg) !important;
  color: var(--retro-text) !important;
  border-color: var(--retro-border) !important;
}

body.retro-mode .status-bar {
  background: var(--retro-panel-bg) !important;
  background-image: none !important;
}

body.retro-mode .status-item:hover {
  background: var(--retro-border) !important;
}

body.retro-mode .legend-item:hover {
  background: var(--retro-border) !important;
}

body.retro-mode #target-list-items li:hover {
  background-color: var(--retro-border) !important;
}

body.retro-mode .loading-overlay {
  background: var(--retro-bg) !important;
}

body.retro-mode .progress-bar::after {
  background: none !important;
}

body.retro-mode button::before {
  background: none !important;
}

body.retro-mode .button-ripple::before {
  background: rgba(0, 255, 0, 0.3) !important;
}

body.retro-mode .loading-enhanced::after {
  background: linear-gradient(90deg, transparent, rgba(0, 255, 0, 0.3), transparent) !important;
}

/* --- Responsive Design --- */
@media (min-width: 992px) {
  /* Desktop layout - Map and controls side by side */
  .map-controls-wrapper {
    flex-direction: row;
    align-items: flex-start;
    justify-content: center; /* Center the content horizontally */
    gap: calc(var(--spacing-unit) * 3); /* 24px gap between map and controls */
  }

  #map-container, #mortar-map-container {
    width: 55%; /* Slightly reduced from 60% */
    margin-bottom: 0;
    position: sticky;
    top: calc(var(--spacing-unit) * 4); /* Stick to the top with some padding */
    max-height: calc(100vh - calc(var(--spacing-unit) * 8)); /* Limit height to viewport minus padding */
    overflow: visible; /* Allow map to overflow if needed */
  }

  #controls-container, #mortar-controls-container {
    width: 40%; /* Slightly increased from 38% */
    max-height: none; /* Allow controls to scroll naturally */
  }

  /* Ensure map stays visible when scrolling through a long control panel */
  #map, #mortar-map {
    position: sticky;
    top: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* Tablet layout - Map above, controls in two columns */
  #controls-container, #mortar-controls-container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: calc(var(--spacing-unit) * 4); /* Increased gap on wider screens */
  }

  .control-panel {
    width: 48%;
  }
}

@media (max-width: 767px) {
  /* Mobile layout - Stack everything vertically */
  body {
    padding: calc(var(--spacing-unit) * 2); /* 16px padding */
  }

  h1 {
    font-size: 2em;
    margin-bottom: calc(var(--spacing-unit) * 2);
  }

  .intro {
    margin-bottom: calc(var(--spacing-unit) * 3);
  }

  .collapsible {
    padding: var(--spacing-unit);
    font-size: 0.9em;
  }

  .content {
    padding: var(--spacing-unit);
  }

  #map, #mortar-map {
    height: 350px;
  }

  #controls-container, #mortar-controls-container {
    padding: calc(var(--spacing-unit) * 3);
  }

  section {
    padding-bottom: calc(var(--spacing-unit) * 2);
  }

  .form-group {
    margin-bottom: calc(var(--spacing-unit) * 1.5);
  }

  label {
    margin-bottom: calc(var(--spacing-unit) * 0.5);
  }

  select, input[type="number"], input[type="text"] {
    padding: var(--spacing-unit);
  }

  /* Enhanced Target List Mobile Responsiveness */
  .target-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .target-location {
    grid-column: 1;
    margin-top: var(--spacing-xs);
  }

  .target-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .target-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .target-actions button {
    width: 100%;
  }

  /* Enhanced Mobile Touch Interactions */
  button, input, select, textarea {
    min-height: 48px; /* Increased from 44px for better touch targets */
  }

  .help-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-left: var(--spacing-sm);
    line-height: 1;
  }

  /* Mobile-specific button spacing */
  .button-group {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .button-group button {
    width: 100%;
    min-height: 52px;
    font-size: 1.1rem;
  }

  /* Mobile navigation improvements */
  .nav-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .nav-button {
    width: 100%;
    min-height: 48px;
    font-size: 1rem;
  }

  /* Mobile tooltip adjustments */
  .tooltip {
    max-width: 90vw;
    font-size: 0.9rem;
    padding: var(--spacing-md);
  }

  /* Mobile form improvements */
  .form-group {
    margin-bottom: var(--spacing-lg);
  }

  label {
    font-size: 1.1rem;
    font-weight: var(--font-weight-semibold);
  }

  /* Mobile status bar */
  .status-bar {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
  }

  .status-item {
    justify-content: center;
    min-height: 40px;
  }

  /* Mobile feedback messages */
  .feedback-message {
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    right: var(--spacing-md);
    transform: none;
    max-width: none;
  }
}

/* --- Additional Mobile Polish --- */

/* Landscape phone optimization */
@media (max-width: 767px) and (orientation: landscape) {
  #map, #mortar-map {
    height: 250px; /* Reduced height for landscape */
  }

  .container {
    padding: var(--spacing-md);
  }

  .collapsible {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  body {
    padding: var(--spacing-md);
  }

  .container {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  h1 {
    font-size: 1.75rem;
  }

  .intro {
    font-size: 0.9rem;
  }

  /* Ultra-mobile button sizing */
  button {
    min-height: 52px;
    font-size: 1rem;
    padding: var(--spacing-md);
  }

  /* Compact target list for small screens */
  #target-list-items li {
    padding: var(--spacing-md);
  }

  .target-details {
    font-size: 0.9rem;
  }

  /* Smaller map for very small screens */
  #map, #mortar-map {
    height: 300px;
  }
}

/* Touch-specific improvements */
@media (pointer: coarse) {
  /* Larger touch targets */
  .leaflet-control-zoom a {
    width: 36px !important;
    height: 36px !important;
    line-height: 36px !important;
  }

  .leaflet-bar a {
    width: 36px !important;
    height: 36px !important;
    line-height: 36px !important;
  }

  /* Enhanced map controls for touch */
  .leaflet-control {
    margin: var(--spacing-md) !important;
  }

  /* Better spacing for touch interactions */
  .target-actions {
    gap: var(--spacing-md);
  }

  .target-actions button {
    min-height: 48px;
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

/* --- Visual Polish & Animations --- */

/* Smooth page transitions */
.container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced button animations */
button {
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease-out, height 0.6s ease-out;
  pointer-events: none;
}

button:active::before {
  width: 300px;
  height: 300px;
}

/* Smooth hover transitions for interactive elements */
.interactive-element,
button,
input,
select,
textarea,
.target-list-item,
.nav-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus animations */
*:focus-visible {
  animation: focusPulse 0.3s ease-out;
}

@keyframes focusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(29, 53, 87, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(29, 53, 87, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(29, 53, 87, 0);
  }
}

/* Staggered animations for lists */
#target-list-items li {
  animation: slideInLeft 0.4s ease-out;
  animation-fill-mode: both;
}

#target-list-items li:nth-child(1) { animation-delay: 0.1s; }
#target-list-items li:nth-child(2) { animation-delay: 0.2s; }
#target-list-items li:nth-child(3) { animation-delay: 0.3s; }
#target-list-items li:nth-child(4) { animation-delay: 0.4s; }
#target-list-items li:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced loading animations */
.loading-spinner {
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Smooth collapsible animations */
.collapsible {
  transition: all 0.3s ease-out;
}

.collapsible:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-medium);
}

.collapsible.active {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(0);
}

.content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s ease-out;
  opacity: 0;
}

.collapsible.active + .content {
  max-height: 1000px; /* Large enough value */
  opacity: 1;
}

/* Enhanced status indicators */
.status-item {
  transition: all 0.3s ease-out;
}

.status-item:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
}

/* Floating action button style for important actions */
#send-targets-button {
  position: relative;
  background: linear-gradient(135deg, var(--action-color), var(--action-dark));
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#send-targets-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
}

#send-targets-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease-out;
}

#send-targets-button:hover::after {
  transform: translateX(100%);
}

/* Enhanced map legend animations */
.legend-item {
  transition: all 0.3s ease-out;
}

.legend-item:hover {
  transform: translateX(5px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs);
}

/* Smooth tooltip animations */
.tooltip {
  animation: tooltipSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes tooltipSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced form field animations */
.form-group {
  position: relative;
}

.form-group input:focus,
.form-group select:focus {
  animation: fieldFocus 0.3s ease-out;
}

@keyframes fieldFocus {
  0% {
    border-left-width: 3px;
  }
  50% {
    border-left-width: 6px;
  }
  100% {
    border-left-width: 3px;
  }
}

/* Micro-interactions for better UX */
.help-icon {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.help-icon:hover {
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-3px) scale(1.1);
  }
  60% {
    transform: translateY(-1px) scale(1.05);
  }
}

/* Enhanced navigation animations */
.nav-button {
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-color);
  transition: width 0.3s ease-out;
}

.nav-button:hover::before,
.nav-button.active::before {
  width: 100%;
}

/* Smooth retro mode transitions */
body.retro-mode * {
  transition: all 0.4s ease-out !important;
}

/* Performance optimizations for animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Touch device optimizations */
@media (pointer: coarse) {
  /* Reduce animation complexity on touch devices */
  button::before {
    display: none;
  }

  .tooltip {
    animation: none;
  }

  /* Simpler hover states for touch */
  button:hover {
    transform: none;
  }
}

/* --- Additional Animation Classes for JavaScript --- */

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.animate-in {
  animation: slideInLeft 0.4s ease-out;
  animation-fill-mode: both;
}

.loading-enhanced {
  position: relative;
  overflow: hidden;
}

.loading-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.field-focused {
  position: relative;
}

.field-focused::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(29, 53, 87, 0.05);
  border-radius: var(--border-radius);
  animation: fieldGlow 0.3s ease-out;
  pointer-events: none;
  z-index: -1;
}

@keyframes fieldGlow {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced button states */
.touch-active {
  transform: scale(0.95) !important;
  transition: transform 0.1s ease-out !important;
}

/* Smooth transitions for dynamic content */
.dynamic-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced success/error states */
.success-state {
  animation: successBounce 0.6s ease-out;
}

.error-state {
  animation: errorShake 0.5s ease-out;
}

@keyframes successBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Subtle parallax effect for container */
.container {
  transform-style: preserve-3d;
}

/* Enhanced map container animations */
#map-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

#map-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-large);
}

/* Smooth legend animations */
#map-legend {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

#map-legend.collapsed {
  transform: translateY(10px);
  opacity: 0.8;
}

#map-legend:not(.collapsed) {
  transform: translateY(0);
  opacity: 1;
}